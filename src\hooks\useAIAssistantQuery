



import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";


const fetchUserById = async (id: number) => {
  const { data, error } = await supabase
    .from('users_with_details')
    .select('*')
    .eq('id', id)
    .single()

  if (error) throw error

  return data
}

export const useUserQuery = (id: number) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => fetchUserById(id),
  })
}
export const useCurrentUserQuery = () => {
  const id = useCurrentUserId()

  return useUserQuery(id)
}